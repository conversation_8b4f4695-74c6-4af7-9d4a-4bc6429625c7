package com.ttv.demo

import android.os.Bundle
import android.util.Log
import android.view.View
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment

/**
 * 主Activity - 模拟客户的真实环境
 * 
 * 这个Activity模拟了客户的主应用环境：
 * 1. 有底部导航栏（main_tab）
 * 2. Fragment容器用于显示HomeFragment
 * 3. 完全模拟客户的实际使用场景
 */
class MainActivityWithNavigation : AppCompatActivity() {

    companion object {
        private const val TAG = "MainActivityWithNav"
    }

    // 底部导航Tab
    private lateinit var tabHome: View
    private lateinit var tabMarket: View
    private lateinit var tabTrade: View
    private lateinit var tabAssets: View
    private lateinit var tabProfile: View

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main_with_navigation)

        Log.d(TAG, "=== 主Activity启动（模拟客户环境）===")

        initViews()
        setupBottomNavigation()
        loadHomeFragment()

        Log.i(TAG, "✓ 主Activity初始化完成，已加载HomeFragment")
    }

    /**
     * 初始化视图组件
     */
    private fun initViews() {
        tabHome = findViewById(R.id.tab_home)
        tabMarket = findViewById(R.id.tab_market)
        tabTrade = findViewById(R.id.tab_trade)
        tabAssets = findViewById(R.id.tab_assets)
        tabProfile = findViewById(R.id.tab_profile)

        Log.d(TAG, "底部导航组件初始化完成")
    }

    /**
     * 设置底部导航点击事件
     */
    private fun setupBottomNavigation() {
        tabHome.setOnClickListener {
            Log.d(TAG, "点击首页Tab")
            selectTab(0)
            loadHomeFragment()
        }

        tabMarket.setOnClickListener {
            Log.d(TAG, "点击行情Tab")
            selectTab(1)
            loadPlaceholderFragment("行情")
        }

        tabTrade.setOnClickListener {
            Log.d(TAG, "点击交易Tab")
            selectTab(2)
            loadPlaceholderFragment("交易")
        }

        tabAssets.setOnClickListener {
            Log.d(TAG, "点击资产Tab")
            selectTab(3)
            loadPlaceholderFragment("资产")
        }

        tabProfile.setOnClickListener {
            Log.d(TAG, "点击我的Tab")
            selectTab(4)
            loadPlaceholderFragment("我的")
        }

        // 默认选中首页
        selectTab(0)

        Log.d(TAG, "底部导航点击事件设置完成")
    }

    /**
     * 选中指定Tab
     */
    private fun selectTab(index: Int) {
        // 重置所有Tab状态
        resetAllTabs()

        // 设置选中状态
        when (index) {
            0 -> setTabSelected(tabHome, true)
            1 -> setTabSelected(tabMarket, true)
            2 -> setTabSelected(tabTrade, true)
            3 -> setTabSelected(tabAssets, true)
            4 -> setTabSelected(tabProfile, true)
        }

        Log.v(TAG, "选中Tab: $index")
    }

    /**
     * 重置所有Tab状态
     */
    private fun resetAllTabs() {
        setTabSelected(tabHome, false)
        setTabSelected(tabMarket, false)
        setTabSelected(tabTrade, false)
        setTabSelected(tabAssets, false)
        setTabSelected(tabProfile, false)
    }

    /**
     * 设置Tab选中状态
     */
    private fun setTabSelected(tab: View, selected: Boolean) {
        val imageView = (tab as android.view.ViewGroup).getChildAt(0) as android.widget.ImageView
        val textView = (tab as android.view.ViewGroup).getChildAt(1) as android.widget.TextView

        if (selected) {
            imageView.setColorFilter(getColor(R.color.blue_primary))
            textView.setTextColor(getColor(R.color.blue_primary))
        } else {
            imageView.setColorFilter(getColor(R.color.text_secondary))
            textView.setTextColor(getColor(R.color.text_secondary))
        }
    }

    /**
     * 加载HomeFragment（客户的核心Fragment）
     */
    private fun loadHomeFragment() {
        Log.d(TAG, "加载HomeFragment...")

        val fragment = HomeDemoFragment()
        
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commit()

        Log.i(TAG, "✓ HomeFragment加载完成")
    }

    /**
     * 加载占位Fragment（其他Tab的模拟）
     */
    private fun loadPlaceholderFragment(title: String) {
        Log.d(TAG, "加载占位Fragment: $title")

        val fragment = PlaceholderFragment.newInstance(title)
        
        supportFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .commit()

        Log.i(TAG, "✓ 占位Fragment加载完成: $title")
    }

    /**
     * 获取底部导航栏高度（供DynamicTabController使用）
     */
    fun getBottomNavigationHeight(): Int {
        val mainTab = findViewById<View>(R.id.main_tab)
        return mainTab?.height ?: 0
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d(TAG, "=== 主Activity销毁 ===")
    }
}

/**
 * 占位Fragment - 用于模拟其他Tab页面
 */
class PlaceholderFragment : Fragment() {
    
    companion object {
        private const val ARG_TITLE = "title"
        
        fun newInstance(title: String): PlaceholderFragment {
            val fragment = PlaceholderFragment()
            val args = Bundle()
            args.putString(ARG_TITLE, title)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: android.view.LayoutInflater,
        container: android.view.ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val title = arguments?.getString(ARG_TITLE) ?: "未知页面"
        
        val textView = android.widget.TextView(requireContext()).apply {
            text = "这是 $title 页面\n\n这里模拟其他Tab的内容\n\n点击底部的「首页」Tab\n可以返回到动态吸底Tab演示"
            textSize = 16f
            gravity = android.view.Gravity.CENTER
            setPadding(32, 32, 32, 32)
        }
        
        return textView
    }
}
