package com.ttv.demo

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.fragment.app.Fragment
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.ttv.demo.dynamic.DynamicTabController

/**
 * HomeFragment演示 - 模拟客户的sf/HomeFragment.kt
 * 
 * 这个Fragment完全模拟客户的HomeFragment环境：
 * 1. 使用客户相同的布局结构
 * 2. 集成动态吸底Tab功能
 * 3. 在有底部导航的Activity中运行
 */
class HomeDemoFragment : Fragment() {

    companion object {
        private const val TAG = "HomeDemoFragment"
    }

    // UI组件 - 对应客户的组件
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var appBarLayout: AppBarLayout
    private lateinit var coIndicator: CoIndicator
    private lateinit var viewPager: ViewPager2

    // 动态Tab控制器
    private var dynamicTabController: DynamicTabController? = null

    // 数据
    private val tabTitles = listOf("推荐", "关注", "热门", "视频", "直播")

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        Log.d(TAG, "=== HomeFragment创建视图 ===")
        return inflater.inflate(R.layout.fragment_home_demo, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        Log.d(TAG, "=== HomeFragment视图创建完成，开始初始化 ===")

        initViews(view)
        setupViewPager()
        setupDynamicTabController()
        setupFluentContainer()

        Log.i(TAG, "✓ HomeFragment初始化完成")
    }

    /**
     * 初始化视图组件
     */
    private fun initViews(view: View) {
        coordinatorLayout = view.findViewById(R.id.clLayout)
        appBarLayout = view.findViewById(R.id.appBarLayout)
        coIndicator = view.findViewById(R.id.coIndicator)
        viewPager = view.findViewById(R.id.home_viewPager)

        Log.d(TAG, "视图组件初始化完成")
        Log.d(TAG, "CoordinatorLayout: ${coordinatorLayout != null}")
        Log.d(TAG, "AppBarLayout: ${appBarLayout != null}")
        Log.d(TAG, "CoIndicator: ${coIndicator != null}")
        Log.d(TAG, "ViewPager: ${viewPager != null}")
    }

    /**
     * 设置ViewPager和Tab
     */
    private fun setupViewPager() {
        Log.d(TAG, "设置ViewPager和Tab...")

        // 设置ViewPager适配器
        val adapter = DemoFragmentAdapter(this, tabTitles)
        viewPager.adapter = adapter

        // 设置CoIndicator与ViewPager的绑定
        TabLayoutMediator(coIndicator, viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()

        Log.d(TAG, "ViewPager和Tab设置完成，Tab数量: ${tabTitles.size}")
    }

    /**
     * 设置动态Tab控制器
     */
    private fun setupDynamicTabController() {
        Log.d(TAG, "=== 设置动态Tab控制器 ===")

        try {
            // 创建动态Tab控制器
            dynamicTabController = DynamicTabController(
                context = requireContext(),
                coordinatorLayout = coordinatorLayout,
                appBarLayout = appBarLayout,
                originalTab = coIndicator,
                viewPager = viewPager,
                tabTitles = tabTitles
            )

            // 初始化控制器
            dynamicTabController?.initialize()

            // 集成业务监听器
            setupBusinessListeners()

            // 启用调试模式
            dynamicTabController?.setDebugMode(true)

            Log.i(TAG, "✓ 动态Tab控制器设置完成")

            // 打印初始状态
            dynamicTabController?.debugStatus("Fragment初始化完成")

        } catch (e: Exception) {
            Log.e(TAG, "✗ 动态Tab控制器设置失败", e)
        }
    }

    /**
     * 设置业务监听器
     */
    private fun setupBusinessListeners() {
        dynamicTabController?.integrateExistingListeners(
            // Tab点击监听器
            tabClickListener = { position ->
                Log.d(TAG, "Tab点击: 位置=$position, 标题=${tabTitles.getOrNull(position)}")
            },

            // Tab选中监听器（用于埋点统计）
            tabSelectedListener = { position ->
                Log.d(TAG, "Tab选中: 位置=$position")
                // 模拟埋点统计
                when (position) {
                    0 -> Log.d(TAG, "埋点: 推荐Tab选中")
                    1 -> Log.d(TAG, "埋点: 关注Tab选中")
                    2 -> Log.d(TAG, "埋点: 热门Tab选中")
                    3 -> Log.d(TAG, "埋点: 视频Tab选中")
                    4 -> Log.d(TAG, "埋点: 直播Tab选中")
                }
            },

            // 重复点击监听器（用于刷新数据）
            refreshDataListener = { position ->
                Log.d(TAG, "Tab重复点击，刷新数据: 位置=$position")
                refreshTabData(position)
            }
        )
    }

    /**
     * 设置fluent容器内容（模拟客户的内容）
     */
    private fun setupFluentContainer() {
        val fluentContainer = view?.findViewById<ViewGroup>(R.id.fluent_container)
        
        // 添加一些模拟内容，让页面可以滚动
        for (i in 1..10) {
            val textView = TextView(requireContext()).apply {
                text = "模拟内容区域 $i\n这里是可滚动的内容\n向上滚动可以看到Tab吸顶效果\n向下滚动可以看到Tab吸底效果"
                textSize = 14f
                setPadding(32, 32, 32, 32)
                setBackgroundColor(if (i % 2 == 0) 0xFFF5F5F5.toInt() else 0xFFFFFFFF.toInt())
            }
            fluentContainer?.addView(textView)
        }

        Log.d(TAG, "fluent容器内容设置完成")
    }

    /**
     * 刷新Tab数据（模拟）
     */
    private fun refreshTabData(position: Int) {
        Log.i(TAG, "刷新${tabTitles.getOrNull(position)}页面数据")
        // 这里可以添加实际的数据刷新逻辑
    }

    override fun onResume() {
        super.onResume()
        Log.d(TAG, "Fragment onResume")

        // 通知控制器生命周期变化
        dynamicTabController?.onLifecycleChanged(true)

        // 重新检测导航栏适配
        dynamicTabController?.reconfigureNavigationBarAdaptation()
    }

    override fun onPause() {
        super.onPause()
        Log.d(TAG, "Fragment onPause")

        // 通知控制器生命周期变化
        dynamicTabController?.onLifecycleChanged(false)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Log.d(TAG, "Fragment onDestroyView")

        // 销毁动态Tab控制器
        dynamicTabController?.destroy()
        dynamicTabController = null

        Log.d(TAG, "=== HomeFragment销毁完成 ===")
    }

    override fun onConfigurationChanged(newConfig: android.content.res.Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d(TAG, "配置变化: ${newConfig.orientation}")

        // 通知控制器配置变化
        dynamicTabController?.onConfigurationChanged()
    }
}

/**
 * ViewPager适配器 - 模拟客户的页面内容
 */
class DemoFragmentAdapter(
    fragment: Fragment,
    private val tabTitles: List<String>
) : androidx.viewpager2.adapter.FragmentStateAdapter(fragment) {

    override fun getItemCount(): Int = tabTitles.size

    override fun createFragment(position: Int): Fragment {
        return DemoPageFragment.newInstance(tabTitles[position], position)
    }
}

/**
 * 页面Fragment - 模拟每个Tab的内容
 */
class DemoPageFragment : Fragment() {

    companion object {
        private const val ARG_TITLE = "title"
        private const val ARG_POSITION = "position"

        fun newInstance(title: String, position: Int): DemoPageFragment {
            val fragment = DemoPageFragment()
            val args = Bundle()
            args.putString(ARG_TITLE, title)
            args.putInt(ARG_POSITION, position)
            fragment.arguments = args
            return fragment
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val title = arguments?.getString(ARG_TITLE) ?: "未知"
        val position = arguments?.getInt(ARG_POSITION) ?: 0

        val textView = TextView(requireContext()).apply {
            text = """
                这是 $title 页面
                位置: $position
                
                🔥 动态吸底Tab演示 🔥
                
                ✨ 功能特性:
                • 向上滚动: Tab自动吸顶
                • 向下滚动: Tab动态吸底
                • 智能切换: 根据滚动方向自动显示
                • 平滑动画: 300ms过渡效果
                • 状态同步: 顶部和底部Tab保持一致
                
                🎯 测试方法:
                1. 向上快速滚动，观察Tab吸顶效果
                2. 向下滚动到底部，观察Tab吸底效果
                3. 点击吸底Tab，观察页面切换和滚动
                4. 重复点击同一Tab，观察刷新逻辑
                
                📱 环境说明:
                • 当前运行在有底部导航的Activity中
                • 完全模拟客户的真实使用环境
                • 底部导航栏高度会被自动检测和适配
            """.trimIndent()
            
            textSize = 14f
            setPadding(32, 32, 32, 32)
            gravity = android.view.Gravity.CENTER
        }

        return textView
    }
}
