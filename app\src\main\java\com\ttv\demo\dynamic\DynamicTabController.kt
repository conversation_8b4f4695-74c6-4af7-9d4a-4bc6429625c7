package com.ttv.demo.dynamic

import android.content.Context
import android.util.Log
import android.view.View
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.ttv.demo.R
import kotlin.math.abs

/**
 * 动态Tab控制器
 * 实现"上滑吸顶，下滑吸底"的Tab位置动态切换效果
 * 
 * 核心功能：
 * 1. 滚动方向检测
 * 2. Tab可见性判断
 * 3. 动态显示/隐藏吸底Tab
 * 4. Tab状态同步
 * 5. 动画控制
 */
class DynamicTabController(
    private val context: Context,
    private val coordinatorLayout: CoordinatorLayout,
    private val appBarLayout: AppBarLayout,
    private val originalTab: CoIndicator,
    private val viewPager: ViewPager2,
    private val tabTitles: List<String>
) {
    
    companion object {
        private const val TAG = "DynamicTabController"
        
        // 滚动方向常量
        const val SCROLL_DIRECTION_NONE = 0
        const val SCROLL_DIRECTION_UP = 1
        const val SCROLL_DIRECTION_DOWN = 2
        
        // 显示阈值
        const val SHOW_BOTTOM_TAB_THRESHOLD = 0.7f  // 滚动70%后显示吸底Tab
        const val ANIMATION_DURATION = 300L
    }
    
    // 动态底部Tab相关组件
    private var bottomTabContainer: View? = null
    private var bottomTab: CoIndicator? = null
    private var tabLayoutMediator: TabLayoutMediator? = null
    
    // 滚动状态跟踪
    private var scrollDirection = SCROLL_DIRECTION_NONE
    private var lastVerticalOffset = 0
    private var isBottomTabVisible = false
    private var isAnimating = false

    // 滚动方向检测优化
    private var scrollDirectionBuffer = mutableListOf<Int>()
    private val bufferSize = 3  // 缓冲区大小，用于平滑滚动方向检测
    private var lastScrollTime = 0L
    private val scrollTimeThreshold = 50L  // 滚动时间阈值，避免过于频繁的检测

    // 边界条件处理
    private var isDestroyed = false
    private var isInitialized = false
    private var fastScrollDetectionCount = 0
    private val fastScrollThreshold = 5  // 快速滚动检测阈值
    private var lastVisibilityCheckTime = 0L
    private val visibilityCheckInterval = 100L  // 可见性检查间隔
    
    // 导航栏适配
    private var bottomNavigationHeight = 0
    
    // 监听器
    private var scrollListener: AppBarLayout.OnOffsetChangedListener? = null

    // 业务监听器集成
    private var onTabClickListener: ((Int) -> Unit)? = null
    private var onTabSelectedListener: ((Int) -> Unit)? = null
    private var onTabReselectedListener: ((Int) -> Unit)? = null
    
    /**
     * 初始化控制器
     */
    fun initialize() {
        Log.d(TAG, "=== 初始化动态Tab控制器 ===")
        
        detectBottomNavigationHeight()
        createBottomTabContainer()
        setupScrollListener()
        setupTabSync()

        isInitialized = true
        Log.i(TAG, "动态Tab控制器初始化完成")
    }

    /**
     * 设置Tab点击监听器（用于集成客户业务逻辑）
     */
    fun setOnTabClickListener(listener: (Int) -> Unit) {
        onTabClickListener = listener
        Log.d(TAG, "设置Tab点击监听器")
    }

    /**
     * 设置Tab选中监听器（用于集成埋点统计等）
     */
    fun setOnTabSelectedListener(listener: (Int) -> Unit) {
        onTabSelectedListener = listener
        Log.d(TAG, "设置Tab选中监听器")
    }

    /**
     * 设置Tab重复选中监听器（用于刷新数据等）
     */
    fun setOnTabReselectedListener(listener: (Int) -> Unit) {
        onTabReselectedListener = listener
        Log.d(TAG, "设置Tab重复选中监听器")
    }

    /**
     * 集成客户现有的监听器系统
     * 这个方法可以直接传入客户现有的监听器逻辑
     */
    fun integrateExistingListeners(
        tabClickListener: ((Int) -> Unit)? = null,
        tabSelectedListener: ((Int) -> Unit)? = null,
        refreshDataListener: ((Int) -> Unit)? = null
    ) {
        Log.d(TAG, "集成现有监听器系统...")

        // 设置点击监听器（用于一般的Tab点击处理）
        tabClickListener?.let { setOnTabClickListener(it) }

        // 设置选中监听器（用于埋点统计等）
        tabSelectedListener?.let { setOnTabSelectedListener(it) }

        // 设置重复点击监听器（用于刷新数据）
        refreshDataListener?.let { setOnTabReselectedListener(it) }

        Log.i(TAG, "现有监听器系统集成完成")
    }

    /**
     * 检查边界条件
     */
    private fun checkBoundaryConditions(): Boolean {
        // 检查是否已销毁
        if (isDestroyed) {
            Log.w(TAG, "控制器已销毁，跳过操作")
            return false
        }

        // 检查是否已初始化
        if (!isInitialized) {
            Log.w(TAG, "控制器未初始化，跳过操作")
            return false
        }

        // 检查关键组件是否存在
        if (bottomTabContainer == null || bottomTab == null) {
            Log.w(TAG, "关键组件缺失，跳过操作")
            return false
        }

        return true
    }

    /**
     * 检测快速滚动
     */
    private fun detectFastScroll(verticalOffset: Int): Boolean {
        val currentOffset = kotlin.math.abs(verticalOffset)
        val offsetDiff = kotlin.math.abs(currentOffset - lastVerticalOffset)

        // 如果偏移量变化很大，认为是快速滚动
        if (offsetDiff > 100) {  // 100px阈值
            fastScrollDetectionCount++
            if (fastScrollDetectionCount >= fastScrollThreshold) {
                Log.d(TAG, "检测到快速滚动，暂停Tab状态变化")
                return true
            }
        } else {
            // 重置快速滚动计数
            if (fastScrollDetectionCount > 0) {
                fastScrollDetectionCount--
            }
        }

        return false
    }

    /**
     * 处理生命周期变化
     */
    fun onLifecycleChanged(isVisible: Boolean) {
        if (!checkBoundaryConditions()) return

        Log.d(TAG, "生命周期变化: 可见=$isVisible")

        if (!isVisible && isBottomTabVisible) {
            // Fragment不可见时隐藏吸底Tab
            Log.d(TAG, "Fragment不可见，隐藏吸底Tab")
            hideBottomTabImmediately()
        }
    }

    /**
     * 处理页面切换
     */
    fun onPageChanged(newPosition: Int) {
        if (!checkBoundaryConditions()) return

        Log.d(TAG, "页面切换到: $newPosition")

        // 确保Tab状态同步
        try {
            val bottomTabLayout = bottomTab ?: return
            if (bottomTabLayout.selectedTabPosition != newPosition) {
                bottomTabLayout.selectTab(bottomTabLayout.getTabAt(newPosition))
            }
        } catch (e: Exception) {
            Log.e(TAG, "页面切换时同步Tab状态失败", e)
        }
    }

    /**
     * 处理配置变化（如屏幕旋转）
     */
    fun onConfigurationChanged() {
        if (!checkBoundaryConditions()) return

        Log.d(TAG, "配置变化，重新检测导航栏")

        // 重新检测导航栏高度
        reconfigureNavigationBarAdaptation()

        // 重新检查Tab可见性
        val currentTime = System.currentTimeMillis()
        if (currentTime - lastVisibilityCheckTime > visibilityCheckInterval) {
            lastVisibilityCheckTime = currentTime

            val isOriginalTabVisible = isOriginalTabInViewport()
            if (isOriginalTabVisible && isBottomTabVisible) {
                Log.d(TAG, "配置变化后原始Tab可见，隐藏吸底Tab")
                hideBottomTabWithAnimation()
            }
        }
    }
    
    /**
     * 检测底部导航栏高度（集成客户现有逻辑）
     */
    private fun detectBottomNavigationHeight() {
        Log.d(TAG, "开始检测底部导航栏高度...")

        bottomNavigationHeight = try {
            // 方法1：检测主应用的底部导航栏（main_tab）
            val mainAppNavigationHeight = detectMainAppNavigationHeight()
            if (mainAppNavigationHeight > 0) {
                Log.d(TAG, "检测到主应用导航栏高度: ${mainAppNavigationHeight}px")
                return@try mainAppNavigationHeight
            }

            // 方法2：检测系统导航栏高度
            val systemNavigationHeight = detectSystemNavigationHeight()
            if (systemNavigationHeight > 0) {
                Log.d(TAG, "检测到系统导航栏高度: ${systemNavigationHeight}px")
                return@try systemNavigationHeight
            }

            // 方法3：使用标准高度作为后备
            val standardHeight = (56 * context.resources.displayMetrics.density).toInt()
            Log.w(TAG, "使用标准导航栏高度: ${standardHeight}px")
            standardHeight

        } catch (e: Exception) {
            Log.e(TAG, "检测导航栏高度失败", e)
            (56 * context.resources.displayMetrics.density).toInt()
        }

        Log.i(TAG, "最终导航栏高度: ${bottomNavigationHeight}px")
    }

    /**
     * 检测主应用导航栏高度（基于客户现有逻辑）
     */
    private fun detectMainAppNavigationHeight(): Int {
        return try {
            // 尝试从Activity中找到main_tab导航栏
            val activity = when (context) {
                is android.app.Activity -> context as android.app.Activity
                is androidx.fragment.app.Fragment -> (context as androidx.fragment.app.Fragment).activity
                else -> null
            } ?: return 0

            val navigationView = activity.findViewById<View>(R.id.main_tab)

            if (navigationView != null) {
                val height = navigationView.height
                val location = IntArray(2)
                navigationView.getLocationOnScreen(location)

                Log.d(TAG, "主应用导航栏检测: 高度=${height}px, 位置=y${location[1]}px")

                if (height > 0) {
                    return height
                }
            } else {
                Log.d(TAG, "未找到主应用导航栏 (main_tab)")
            }

            0
        } catch (e: Exception) {
            Log.w(TAG, "检测主应用导航栏失败", e)
            0
        }
    }

    /**
     * 检测系统导航栏高度
     */
    private fun detectSystemNavigationHeight(): Int {
        return try {
            val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                context.resources.getDimensionPixelSize(resourceId)
            } else {
                0
            }
        } catch (e: Exception) {
            Log.w(TAG, "检测系统导航栏高度失败", e)
            0
        }
    }

    /**
     * 动态重新检测导航栏高度（用于处理导航栏变化）
     */
    fun reconfigureNavigationBarAdaptation() {
        Log.d(TAG, "=== 重新配置导航栏适配 ===")

        val oldHeight = bottomNavigationHeight
        detectBottomNavigationHeight()

        if (oldHeight != bottomNavigationHeight) {
            Log.d(TAG, "导航栏高度变化: ${oldHeight}px -> ${bottomNavigationHeight}px")

            // 重新配置底部Tab容器的padding
            bottomTabContainer?.setPadding(
                bottomTabContainer?.paddingLeft ?: 0,
                bottomTabContainer?.paddingTop ?: 0,
                bottomTabContainer?.paddingRight ?: 0,
                bottomNavigationHeight
            )

            Log.i(TAG, "导航栏适配重新配置完成")
        } else {
            Log.d(TAG, "导航栏高度未变化，无需重新配置")
        }
    }
    
    /**
     * 创建底部Tab容器
     */
    private fun createBottomTabContainer() {
        Log.d(TAG, "创建动态底部Tab容器...")

        // 从CoordinatorLayout中找到动态底部Tab容器
        bottomTabContainer = coordinatorLayout.findViewById(R.id.dynamic_bottom_tab_container)
        bottomTab = coordinatorLayout.findViewById(R.id.dynamic_bottom_tab)

        if (bottomTabContainer == null || bottomTab == null) {
            Log.e(TAG, "未找到动态底部Tab组件，请检查布局文件")
            return
        }

        // 设置初始状态
        bottomTabContainer?.apply {
            visibility = View.GONE
            translationY = height.toFloat()
            alpha = 0f
        }

        // 适配底部导航栏
        if (bottomNavigationHeight > 0) {
            bottomTabContainer?.setPadding(
                bottomTabContainer?.paddingLeft ?: 0,
                bottomTabContainer?.paddingTop ?: 0,
                bottomTabContainer?.paddingRight ?: 0,
                bottomNavigationHeight
            )
        }

        Log.d(TAG, "底部Tab容器创建完成，导航栏适配: ${bottomNavigationHeight}px")
    }
    
    /**
     * 设置滚动监听器
     */
    private fun setupScrollListener() {
        Log.d(TAG, "设置滚动监听器...")
        
        scrollListener = AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            // 边界条件检查
            if (!checkBoundaryConditions()) return@OnOffsetChangedListener
            if (isAnimating) return@OnOffsetChangedListener

            // 快速滚动检测
            if (detectFastScroll(verticalOffset)) {
                return@OnOffsetChangedListener
            }

            detectScrollDirection(verticalOffset)
            handleTabVisibilityLogic(verticalOffset)
        }
        
        appBarLayout.addOnOffsetChangedListener(scrollListener)
        Log.d(TAG, "滚动监听器设置完成")
    }
    
    /**
     * 检测滚动方向（优化版本，使用缓冲区平滑检测）
     */
    private fun detectScrollDirection(verticalOffset: Int) {
        val currentTime = System.currentTimeMillis()
        val currentOffset = abs(verticalOffset)

        // 时间阈值检查，避免过于频繁的检测
        if (currentTime - lastScrollTime < scrollTimeThreshold) {
            return
        }
        lastScrollTime = currentTime

        // 计算当前滚动方向
        val currentDirection = when {
            currentOffset > lastVerticalOffset -> SCROLL_DIRECTION_UP
            currentOffset < lastVerticalOffset -> SCROLL_DIRECTION_DOWN
            else -> SCROLL_DIRECTION_NONE
        }

        // 添加到缓冲区
        scrollDirectionBuffer.add(currentDirection)
        if (scrollDirectionBuffer.size > bufferSize) {
            scrollDirectionBuffer.removeAt(0)
        }

        // 基于缓冲区确定最终滚动方向
        val finalDirection = determineFinalScrollDirection()

        // 只有方向真正改变时才更新
        if (finalDirection != scrollDirection) {
            val oldDirection = scrollDirection
            scrollDirection = finalDirection

            Log.d(TAG, "滚动方向变化: ${getScrollDirectionString(oldDirection)} -> ${getScrollDirectionString(scrollDirection)}")
            Log.v(TAG, "当前偏移: ${currentOffset}px, 缓冲区: ${scrollDirectionBuffer.map { getScrollDirectionString(it) }}")
        }

        lastVerticalOffset = currentOffset
    }

    /**
     * 基于缓冲区确定最终滚动方向
     */
    private fun determineFinalScrollDirection(): Int {
        if (scrollDirectionBuffer.isEmpty()) return SCROLL_DIRECTION_NONE

        val upCount = scrollDirectionBuffer.count { it == SCROLL_DIRECTION_UP }
        val downCount = scrollDirectionBuffer.count { it == SCROLL_DIRECTION_DOWN }
        val noneCount = scrollDirectionBuffer.count { it == SCROLL_DIRECTION_NONE }

        return when {
            upCount > downCount && upCount > noneCount -> SCROLL_DIRECTION_UP
            downCount > upCount && downCount > noneCount -> SCROLL_DIRECTION_DOWN
            else -> SCROLL_DIRECTION_NONE
        }
    }
    
    /**
     * 处理Tab可见性逻辑（增强版本）
     */
    private fun handleTabVisibilityLogic(verticalOffset: Int) {
        val isOriginalTabVisible = isOriginalTabInViewport()
        val isTabDisappearedFromBottom = isTabDisappearedFromBottom()
        val isTabSticky = checkIfTabIsSticky()
        val scrollPercentage = abs(verticalOffset).toFloat() / appBarLayout.totalScrollRange

        Log.v(TAG, """
            |Tab状态检查:
            |  - 原始Tab可见: $isOriginalTabVisible
            |  - 从底部消失: $isTabDisappearedFromBottom
            |  - 已吸顶: $isTabSticky
            |  - 滚动百分比: ${String.format("%.1f", scrollPercentage * 100)}%
            |  - 当前滚动方向: ${getScrollDirectionString(scrollDirection)}
        """.trimMargin())

        when (scrollDirection) {
            SCROLL_DIRECTION_UP -> {
                // 向上滚动：Tab吸顶时隐藏吸底Tab
                if (isTabSticky && isBottomTabVisible) {
                    Log.d(TAG, "向上滚动，Tab已吸顶，隐藏吸底Tab")
                    hideBottomTabWithAnimation()
                }
            }

            SCROLL_DIRECTION_DOWN -> {
                // 向下滚动：根据不同情况决定是否显示吸底Tab
                when {
                    // 情况1：Tab从底部消失，且滚动足够多时显示吸底Tab
                    isTabDisappearedFromBottom &&
                    scrollPercentage > SHOW_BOTTOM_TAB_THRESHOLD &&
                    !isBottomTabVisible -> {
                        Log.d(TAG, "向下滚动，Tab从底部消失且滚动充分，显示吸底Tab")
                        showBottomTabWithAnimation()
                    }

                    // 情况2：Tab重新可见时隐藏吸底Tab
                    isOriginalTabVisible && isBottomTabVisible -> {
                        Log.d(TAG, "向下滚动，原始Tab重新可见，隐藏吸底Tab")
                        hideBottomTabWithAnimation()
                    }

                    // 情况3：Tab不再吸顶且可见时隐藏吸底Tab
                    !isTabSticky && isOriginalTabVisible && isBottomTabVisible -> {
                        Log.d(TAG, "向下滚动，Tab不再吸顶且可见，隐藏吸底Tab")
                        hideBottomTabWithAnimation()
                    }
                }
            }

            SCROLL_DIRECTION_NONE -> {
                // 静止状态：检查是否需要调整Tab状态
                if (!isOriginalTabVisible && !isTabSticky && isTabDisappearedFromBottom && !isBottomTabVisible) {
                    Log.d(TAG, "静止状态，Tab不可见且从底部消失，显示吸底Tab")
                    showBottomTabWithAnimation()
                }
            }
        }
    }
    
    /**
     * 检查原始Tab是否在可视区域内（增强版本）
     */
    private fun isOriginalTabInViewport(): Boolean {
        // 获取原始Tab在屏幕中的位置
        val tabLocation = IntArray(2)
        originalTab.getLocationOnScreen(tabLocation)

        // 获取CoordinatorLayout在屏幕中的位置（作为参考容器）
        val containerLocation = IntArray(2)
        coordinatorLayout.getLocationOnScreen(containerLocation)

        val screenHeight = context.resources.displayMetrics.heightPixels
        val tabTop = tabLocation[1]
        val tabBottom = tabTop + originalTab.height
        val containerTop = containerLocation[1]
        val containerBottom = containerTop + coordinatorLayout.height

        // 检查Tab是否在屏幕可视区域内
        val isInScreen = tabTop >= 0 && tabBottom <= screenHeight

        // 检查Tab是否在容器可视区域内
        val isInContainer = tabTop >= containerTop && tabBottom <= containerBottom

        // 检查Tab是否被AppBarLayout完全遮挡（即已经吸顶）
        val isSticky = checkIfTabIsSticky()

        val isVisible = isInScreen && isInContainer && !isSticky

        Log.v(TAG, """
            |Tab可见性检查详情:
            |  - Tab位置: top=${tabTop}px, bottom=${tabBottom}px
            |  - 容器位置: top=${containerTop}px, bottom=${containerBottom}px
            |  - 屏幕高度: ${screenHeight}px
            |  - 在屏幕内: $isInScreen
            |  - 在容器内: $isInContainer
            |  - 已吸顶: $isSticky
            |  - 最终可见: $isVisible
        """.trimMargin())

        return isVisible
    }

    /**
     * 检查Tab是否已经吸顶（被AppBarLayout行为影响）
     */
    private fun checkIfTabIsSticky(): Boolean {
        val totalScrollRange = appBarLayout.totalScrollRange
        val currentOffset = abs(lastVerticalOffset)

        // 如果滚动偏移量接近总滚动范围，说明Tab已经吸顶
        val stickyThreshold = totalScrollRange * 0.9f  // 90%阈值
        val isSticky = currentOffset >= stickyThreshold

        Log.v(TAG, "吸顶检查: 当前偏移=${currentOffset}px, 总范围=${totalScrollRange}px, 阈值=${stickyThreshold}px, 已吸顶=$isSticky")

        return isSticky
    }

    /**
     * 检查Tab是否从底部消失（用于判断是否显示吸底Tab）
     */
    private fun isTabDisappearedFromBottom(): Boolean {
        val tabLocation = IntArray(2)
        originalTab.getLocationOnScreen(tabLocation)

        val screenHeight = context.resources.displayMetrics.heightPixels
        val tabTop = tabLocation[1]

        // 如果Tab的顶部位置超过了屏幕高度，说明从底部消失
        val disappearedFromBottom = tabTop > screenHeight

        Log.v(TAG, "底部消失检查: Tab顶部=${tabTop}px, 屏幕高度=${screenHeight}px, 从底部消失=$disappearedFromBottom")

        return disappearedFromBottom
    }
    
    /**
     * 显示底部Tab（带动画）
     */
    private fun showBottomTabWithAnimation() {
        val container = bottomTabContainer ?: return

        // 防止重复动画
        if (isAnimating || isBottomTabVisible) {
            Log.v(TAG, "跳过显示动画：正在动画中($isAnimating) 或 已经可见($isBottomTabVisible)")
            return
        }

        Log.d(TAG, "开始显示吸底Tab动画")
        isAnimating = true

        // 确保容器可见并设置初始状态
        container.visibility = View.VISIBLE
        container.alpha = 0f

        // 计算初始位置（从底部滑入）
        val initialTranslationY = container.height.toFloat() + bottomNavigationHeight
        container.translationY = initialTranslationY

        // 执行动画
        container.animate()
            .translationY(0f)
            .alpha(1f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(android.view.animation.DecelerateInterpolator())
            .withStartAction {
                Log.v(TAG, "吸底Tab显示动画开始")
            }
            .withEndAction {
                isBottomTabVisible = true
                isAnimating = false
                Log.i(TAG, "✓ 吸底Tab显示完成")
            }
            .start()
    }

    /**
     * 隐藏底部Tab（带动画）
     */
    private fun hideBottomTabWithAnimation() {
        val container = bottomTabContainer ?: return

        // 防止重复动画
        if (isAnimating || !isBottomTabVisible) {
            Log.v(TAG, "跳过隐藏动画：正在动画中($isAnimating) 或 已经隐藏(!$isBottomTabVisible)")
            return
        }

        Log.d(TAG, "开始隐藏吸底Tab动画")
        isAnimating = true

        // 计算目标位置（滑出到底部）
        val targetTranslationY = container.height.toFloat() + bottomNavigationHeight

        // 执行动画
        container.animate()
            .translationY(targetTranslationY)
            .alpha(0f)
            .setDuration(ANIMATION_DURATION)
            .setInterpolator(android.view.animation.AccelerateInterpolator())
            .withStartAction {
                Log.v(TAG, "吸底Tab隐藏动画开始")
            }
            .withEndAction {
                container.visibility = View.GONE
                isBottomTabVisible = false
                isAnimating = false
                Log.i(TAG, "✓ 吸底Tab隐藏完成")
            }
            .start()
    }

    /**
     * 立即显示底部Tab（无动画）
     */
    private fun showBottomTabImmediately() {
        val container = bottomTabContainer ?: return

        Log.d(TAG, "立即显示吸底Tab（无动画）")

        container.clearAnimation()
        container.visibility = View.VISIBLE
        container.alpha = 1f
        container.translationY = 0f

        isBottomTabVisible = true
        isAnimating = false

        Log.i(TAG, "✓ 吸底Tab立即显示完成")
    }

    /**
     * 立即隐藏底部Tab（无动画）
     */
    private fun hideBottomTabImmediately() {
        val container = bottomTabContainer ?: return

        Log.d(TAG, "立即隐藏吸底Tab（无动画）")

        container.clearAnimation()
        container.visibility = View.GONE
        container.alpha = 0f
        container.translationY = container.height.toFloat() + bottomNavigationHeight

        isBottomTabVisible = false
        isAnimating = false

        Log.i(TAG, "✓ 吸底Tab立即隐藏完成")
    }
    
    /**
     * 设置Tab同步机制
     */
    private fun setupTabSync() {
        Log.d(TAG, "设置Tab同步机制...")

        val bottomTabLayout = bottomTab ?: run {
            Log.e(TAG, "底部Tab未找到，无法设置同步")
            return
        }

        // 1. 设置底部Tab与ViewPager的双向绑定
        tabLayoutMediator = TabLayoutMediator(bottomTabLayout, viewPager) { tab, position ->
            tab.text = tabTitles.getOrNull(position) ?: "Tab $position"
        }
        tabLayoutMediator?.attach()

        // 2. 设置底部Tab点击监听器
        bottomTabLayout.addOnTabSelectedListener(object : TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let { selectedTab ->
                    val position = selectedTab.position
                    Log.d(TAG, "底部Tab选中: 位置=$position, 标题=${selectedTab.text}")

                    // 同步到ViewPager
                    if (viewPager.currentItem != position) {
                        viewPager.setCurrentItem(position, true)
                    }

                    // 同步到原始Tab
                    syncToOriginalTab(position)

                    // 触发业务监听器
                    onTabSelectedListener?.invoke(position)

                    // 执行点击后的滚动行为
                    performScrollToOriginalTab()
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // Tab取消选中时的处理
                Log.v(TAG, "底部Tab取消选中: ${tab?.position}")
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                tab?.let { selectedTab ->
                    val position = selectedTab.position
                    Log.d(TAG, "底部Tab重复点击: 位置=$position")

                    // 触发重复点击监听器（用于刷新数据等业务逻辑）
                    onTabReselectedListener?.invoke(position)

                    // 触发通用点击监听器
                    onTabClickListener?.invoke(position)

                    // 重复点击时滚动到原始Tab位置
                    performScrollToOriginalTab()
                }
            }
        })

        // 3. 监听ViewPager页面变化，同步到底部Tab
        viewPager.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)

                Log.v(TAG, "ViewPager页面变化: $position")

                // 同步到底部Tab（避免循环调用）
                if (bottomTabLayout.selectedTabPosition != position) {
                    bottomTabLayout.selectTab(bottomTabLayout.getTabAt(position))
                }

                // 同步到原始Tab
                syncToOriginalTab(position)
            }
        })

        Log.d(TAG, "Tab同步机制设置完成")
    }

    /**
     * 同步选中状态到原始Tab
     */
    private fun syncToOriginalTab(position: Int) {
        try {
            // 获取原始Tab的指定位置Tab
            val originalTabCount = originalTab.tabCount
            if (position >= 0 && position < originalTabCount) {
                val targetTab = originalTab.getTabAt(position)
                if (targetTab != null && !targetTab.isSelected) {
                    Log.v(TAG, "同步到原始Tab: 位置=$position")
                    targetTab.select()
                }
            } else {
                Log.w(TAG, "无效的Tab位置: $position, 原始Tab数量: $originalTabCount")
            }
        } catch (e: Exception) {
            Log.e(TAG, "同步到原始Tab失败", e)
        }
    }

    /**
     * 点击吸底Tab后滚动到原始Tab位置
     */
    private fun performScrollToOriginalTab() {
        try {
            Log.d(TAG, "执行滚动到原始Tab位置")

            // 计算目标滚动位置（滚动到屏幕1/3位置）
            val screenHeight = context.resources.displayMetrics.heightPixels
            val targetPosition = (screenHeight * 0.33f).toInt()

            // 获取原始Tab的当前位置
            val tabLocation = IntArray(2)
            originalTab.getLocationOnScreen(tabLocation)
            val currentTabY = tabLocation[1]

            // 计算需要滚动的距离
            val scrollDistance = currentTabY - targetPosition

            Log.d(TAG, "滚动计算: 当前Tab位置=${currentTabY}px, 目标位置=${targetPosition}px, 滚动距离=${scrollDistance}px")

            // 执行平滑滚动（这里需要根据实际的滚动容器来实现）
            // 由于使用的是CoordinatorLayout + AppBarLayout，可以通过AppBarLayout的滚动来实现
            if (scrollDistance > 0) {
                // 需要向上滚动
                appBarLayout.setExpanded(false, true)
            } else {
                // 需要向下滚动
                appBarLayout.setExpanded(true, true)
            }

        } catch (e: Exception) {
            Log.e(TAG, "滚动到原始Tab位置失败", e)
        }
    }
    
    /**
     * 获取滚动方向字符串
     */
    private fun getScrollDirectionString(direction: Int): String {
        return when (direction) {
            SCROLL_DIRECTION_UP -> "向上"
            SCROLL_DIRECTION_DOWN -> "向下"
            else -> "静止"
        }
    }
    
    /**
     * 销毁控制器，释放资源
     */
    fun destroy() {
        Log.d(TAG, "=== 销毁动态Tab控制器 ===")

        // 标记为已销毁
        isDestroyed = true
        isInitialized = false

        // 立即隐藏吸底Tab
        if (isBottomTabVisible) {
            hideBottomTabImmediately()
        }

        // 移除滚动监听器
        scrollListener?.let { appBarLayout.removeOnOffsetChangedListener(it) }
        scrollListener = null

        // 销毁TabLayoutMediator
        tabLayoutMediator?.detach()
        tabLayoutMediator = null

        // 清理监听器
        onTabClickListener = null
        onTabSelectedListener = null
        onTabReselectedListener = null

        // 清理缓冲区
        scrollDirectionBuffer.clear()

        // 清理引用
        bottomTabContainer = null
        bottomTab = null

        Log.i(TAG, "动态Tab控制器销毁完成")
    }
    
    /**
     * 获取当前状态信息（用于调试）
     */
    fun getStatusInfo(): String {
        return """
            |=== 动态Tab控制器状态 ===
            |基础状态:
            |  - 已初始化: $isInitialized
            |  - 已销毁: $isDestroyed
            |  - 滚动方向: ${getScrollDirectionString(scrollDirection)}
            |  - 最后偏移: ${lastVerticalOffset}px
            |  - 吸底Tab可见: $isBottomTabVisible
            |  - 动画中: $isAnimating
            |
            |组件状态:
            |  - 底部Tab容器: ${if (bottomTabContainer != null) "存在" else "缺失"}
            |  - 底部Tab: ${if (bottomTab != null) "存在" else "缺失"}
            |  - TabLayoutMediator: ${if (tabLayoutMediator != null) "已绑定" else "未绑定"}
            |
            |滚动检测:
            |  - 滚动缓冲区: ${scrollDirectionBuffer.map { getScrollDirectionString(it) }}
            |  - 快速滚动计数: $fastScrollDetectionCount
            |  - 上次滚动时间: ${System.currentTimeMillis() - lastScrollTime}ms前
            |
            |可见性检测:
            |  - 原始Tab可见: ${if (checkBoundaryConditions()) isOriginalTabInViewport() else "无法检测"}
            |  - Tab已吸顶: ${if (checkBoundaryConditions()) checkIfTabIsSticky() else "无法检测"}
            |  - 从底部消失: ${if (checkBoundaryConditions()) isTabDisappearedFromBottom() else "无法检测"}
            |
            |导航栏适配:
            |  - 导航栏高度: ${bottomNavigationHeight}px
            |  - 主应用导航栏: ${detectMainAppNavigationHeight()}px
            |  - 系统导航栏: ${detectSystemNavigationHeight()}px
            |
            |监听器状态:
            |  - 点击监听器: ${if (onTabClickListener != null) "已设置" else "未设置"}
            |  - 选中监听器: ${if (onTabSelectedListener != null) "已设置" else "未设置"}
            |  - 重复选中监听器: ${if (onTabReselectedListener != null) "已设置" else "未设置"}
            |=== 状态信息结束 ===
        """.trimMargin()
    }

    /**
     * 调试方法：打印详细状态信息
     */
    fun debugStatus(action: String = "状态检查") {
        Log.d(TAG, "=== 调试状态 [$action] ===")
        Log.d(TAG, getStatusInfo())

        // 额外的组件详细信息
        debugComponentDetails()

        // 滚动状态详细信息
        debugScrollState()

        Log.d(TAG, "=== 调试状态结束 ===")
    }

    /**
     * 调试组件详细信息
     */
    private fun debugComponentDetails() {
        Log.d(TAG, "--- 组件详细信息 ---")

        // 原始Tab详细信息
        try {
            val location = IntArray(2)
            originalTab.getLocationOnScreen(location)

            Log.d(TAG, "原始Tab (CoIndicator):")
            Log.d(TAG, "  - 位置: x=${location[0]}px, y=${location[1]}px")
            Log.d(TAG, "  - 尺寸: 宽=${originalTab.width}px, 高=${originalTab.height}px")
            Log.d(TAG, "  - 可见性: ${if (originalTab.visibility == View.VISIBLE) "可见" else "隐藏"}")
            Log.d(TAG, "  - Tab数量: ${originalTab.tabCount}")
            Log.d(TAG, "  - 当前选中: ${originalTab.selectedTabPosition}")
        } catch (e: Exception) {
            Log.w(TAG, "获取原始Tab信息失败", e)
        }

        // 底部Tab详细信息
        bottomTabContainer?.let { container ->
            try {
                val location = IntArray(2)
                container.getLocationOnScreen(location)

                Log.d(TAG, "底部Tab容器:")
                Log.d(TAG, "  - 位置: x=${location[0]}px, y=${location[1]}px")
                Log.d(TAG, "  - 尺寸: 宽=${container.width}px, 高=${container.height}px")
                Log.d(TAG, "  - 可见性: ${if (container.visibility == View.VISIBLE) "可见" else "隐藏"}")
                Log.d(TAG, "  - Alpha: ${container.alpha}")
                Log.d(TAG, "  - TranslationY: ${container.translationY}px")

                bottomTab?.let { tab ->
                    Log.d(TAG, "底部Tab:")
                    Log.d(TAG, "  - Tab数量: ${tab.tabCount}")
                    Log.d(TAG, "  - 当前选中: ${tab.selectedTabPosition}")
                }
            } catch (e: Exception) {
                Log.w(TAG, "获取底部Tab信息失败", e)
            }
        }

        // ViewPager详细信息
        try {
            Log.d(TAG, "ViewPager2:")
            Log.d(TAG, "  - 当前页面: ${viewPager.currentItem}")
            Log.d(TAG, "  - 总页面数: ${viewPager.adapter?.itemCount ?: 0}")
            Log.d(TAG, "  - 滚动状态: ${getViewPagerScrollStateString(viewPager.scrollState)}")
        } catch (e: Exception) {
            Log.w(TAG, "获取ViewPager信息失败", e)
        }
    }

    /**
     * 调试滚动状态信息
     */
    private fun debugScrollState() {
        Log.d(TAG, "--- 滚动状态信息 ---")

        try {
            val totalScrollRange = appBarLayout.totalScrollRange
            val scrollPercentage = if (totalScrollRange > 0) {
                (lastVerticalOffset.toFloat() / totalScrollRange * 100)
            } else 0f

            Log.d(TAG, "AppBarLayout滚动:")
            Log.d(TAG, "  - 总滚动范围: ${totalScrollRange}px")
            Log.d(TAG, "  - 当前偏移: ${lastVerticalOffset}px")
            Log.d(TAG, "  - 滚动百分比: ${String.format("%.1f", scrollPercentage)}%")
            Log.d(TAG, "  - 子View数量: ${appBarLayout.childCount}")

            // CoordinatorLayout状态
            Log.d(TAG, "CoordinatorLayout:")
            Log.d(TAG, "  - 子View数量: ${coordinatorLayout.childCount}")
            Log.d(TAG, "  - 尺寸: 宽=${coordinatorLayout.width}px, 高=${coordinatorLayout.height}px")

        } catch (e: Exception) {
            Log.w(TAG, "获取滚动状态失败", e)
        }
    }

    /**
     * 获取ViewPager滚动状态字符串
     */
    private fun getViewPagerScrollStateString(scrollState: Int): String {
        return when (scrollState) {
            ViewPager2.SCROLL_STATE_IDLE -> "IDLE(空闲)"
            ViewPager2.SCROLL_STATE_DRAGGING -> "DRAGGING(拖拽中)"
            ViewPager2.SCROLL_STATE_SETTLING -> "SETTLING(自动滚动中)"
            else -> "UNKNOWN($scrollState)"
        }
    }

    /**
     * 启用/禁用调试模式
     */
    fun setDebugMode(enabled: Boolean) {
        // 这里可以控制日志级别
        Log.d(TAG, "调试模式: ${if (enabled) "启用" else "禁用"}")
    }
}
