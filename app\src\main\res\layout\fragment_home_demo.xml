<?xml version="1.0" encoding="utf-8"?>
<com.hbg.lib.widgets.LoadingRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_content"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="?attr/Background_L1"
    android:orientation="vertical"
    tools:ignore="MissingDefaultResource">

    <!--给缓存WebView准备的ViewGroup-->
    <FrameLayout
        android:id="@+id/webview_pool_container"
        android:layout_width="0dp"
        android:layout_height="0dp" />

    <!--navigation 区域 状态栏和引擎容器-->
    <RelativeLayout
        android:id="@+id/navigation_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        tools:ignore="MissingDefaultResource">

        <!--状态栏-->
        <View
            android:id="@+id/home_status_bar"
            android:layout_width="match_parent"
            android:layout_height="@dimen/dimen_25"
            android:scaleType="centerCrop" />

        <!--navigation 容器区域-->
        <LinearLayout
            android:id="@+id/home_navigation_ll"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/home_status_bar"
            android:orientation="vertical">

        </LinearLayout>

    </RelativeLayout>

    <!-- fluent 页面流模块区域 -->
    <com.scwang.smart.refresh.layout.SmartRefreshLayout
        android:id="@+id/fluent_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/navigation_container"
        android:layout_marginTop="@dimen/dimen_12">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/clLayout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <com.google.android.material.appbar.AppBarLayout
                android:id="@+id/appBarLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/transparent"
                android:gravity="center_horizontal"
                android:orientation="vertical"
                app:elevation="0dp"
                app:layout_behavior="com.hbg.module.libkt.custom.coord.AppBarLayoutBehavior">

                <com.huobi.view.MyNestedScrollView
                    android:id="@+id/fluent_content_nsv"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:descendantFocusability="blocksDescendants"
                    android:fillViewport="true"
                    android:focusable="true"
                    android:focusableInTouchMode="true"
                    android:overScrollMode="never"
                    android:scrollbars="none"
                    app:layout_scrollFlags="scroll">

                    <LinearLayout
                        android:id="@+id/fluent_container"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical">

                        <!--error布局-->
                        <!--                    <include layout="@layout/part_home_index_error" />-->

                    </LinearLayout>
                </com.huobi.view.MyNestedScrollView>

                <!--  feed tab 组件 -->
                <LinearLayout
                    android:id="@+id/home_feed_linear_tabLayout"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    app:layout_scrollFlags="scroll|enterAlways"
                    app:layout_isSticky="true">

                    <com.hbg.module.libkt.custom.indicator.CoIndicator
                        android:id="@+id/coIndicator"
                        android:layout_width="0dp"
                        android:layout_height="@dimen/dimen_40"
                        android:layout_weight="1"
                        android:paddingLeft="@dimen/dimen_8"
                        app:tabGravity="start"
                        android:foregroundGravity="bottom"
                        app:tabIndicator="@drawable/home_feed_tab_indicator"
                        app:tabIndicatorColor="?attr/Button_Blue_Fill"
                        app:tabIndicatorHeight="3dp"
                        app:tabMaxWidth="@dimen/dimen_200"
                        app:tabMinWidth="@dimen/dimen_20"
                        app:tabMode="scrollable"
                        app:tabPaddingEnd="@dimen/dimen_8"
                        app:tabPaddingStart="@dimen/dimen_8"
                        app:tabRippleColor="@android:color/transparent"
                        app:tabSelectedTextColor="?attr/Button_Blue_Fill"
                        app:tabTextAppearance="@style/FeedTabLayoutStyle"
                        app:tabTextColor="?attr/Text_L3" />

                    <!--  TabLayout 发布按钮  -->
                    <com.huobi.view.roundview.RoundLinearLayout
                        android:id="@+id/home_release_btn"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/dimen_20"
                        android:layout_marginEnd="@dimen/dimen_16"
                        android:gravity="center"
                        android:orientation="horizontal"
                        android:paddingStart="@dimen/dimen_10"
                        android:paddingEnd="@dimen/dimen_10"
                        android:visibility="gone"
                        app:rv_backgroundColor="@color/otc_fitter_item_unselect_color"
                        app:rv_cornerRadius="@dimen/global_corner_radius_2"
                        app:rv_isRippleEnable="false">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="@dimen/dimen_20"
                            android:gravity="center"
                            android:text="@string/n_send_comment"
                            android:textColor="?attr/Button_Blue_Fill"
                            android:textSize="@dimen/global_text_size_12" />
                    </com.huobi.view.roundview.RoundLinearLayout>
                </LinearLayout>
            </com.google.android.material.appbar.AppBarLayout>
            
            <!--  feed view pager 组件 -->
            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/home_viewPager"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:fadingEdge="none"
                android:overScrollMode="never"
                android:scrollbars="none"
                app:layout_behavior="@string/appbar_scrolling_view_behavior"/>

            <!-- 动态吸底Tab容器 - 仅在需要时显示 -->
            <LinearLayout
                android:id="@+id/dynamic_bottom_tab_container"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:orientation="vertical"
                android:background="?attr/Background_L1"
                android:elevation="12dp"
                android:visibility="gone"
                android:translationY="100dp">

                <!-- 分割线 -->
                <View
                    android:layout_width="match_parent"
                    android:layout_height="1dp"
                    android:background="?attr/Text_L3"
                    android:alpha="0.2" />

                <!-- 动态Tab导航栏 - 使用与原始Tab相同的CoIndicator -->
                <com.hbg.module.libkt.custom.indicator.CoIndicator
                    android:id="@+id/dynamic_bottom_tab"
                    android:layout_width="match_parent"
                    android:layout_height="@dimen/dimen_40"
                    android:paddingLeft="@dimen/dimen_8"
                    app:tabGravity="start"
                    android:foregroundGravity="bottom"
                    app:tabIndicator="@drawable/home_feed_tab_indicator"
                    app:tabIndicatorColor="?attr/Button_Blue_Fill"
                    app:tabIndicatorHeight="3dp"
                    app:tabMaxWidth="@dimen/dimen_200"
                    app:tabMinWidth="@dimen/dimen_20"
                    app:tabMode="scrollable"
                    app:tabPaddingEnd="@dimen/dimen_8"
                    app:tabPaddingStart="@dimen/dimen_8"
                    app:tabRippleColor="@android:color/transparent"
                    app:tabSelectedTextColor="?attr/Button_Blue_Fill"
                    app:tabTextAppearance="@style/FeedTabLayoutStyle"
                    app:tabTextColor="?attr/Text_L3" />

            </LinearLayout>

        </androidx.coordinatorlayout.widget.CoordinatorLayout>
    </com.scwang.smart.refresh.layout.SmartRefreshLayout>

    <!--动画层-->
    <RelativeLayout
        android:id="@+id/rl_new_hand_area_animation_layer"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />
</com.hbg.lib.widgets.LoadingRelativeLayout>
