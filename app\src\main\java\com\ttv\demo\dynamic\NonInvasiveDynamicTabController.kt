package com.ttv.demo.dynamic

import android.content.Context
import android.util.Log
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import android.widget.LinearLayout
import androidx.coordinatorlayout.widget.CoordinatorLayout
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.appbar.AppBarLayout
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.ttv.demo.R
import kotlin.math.abs

/**
 * 非侵入式动态Tab控制器
 * 
 * 特点：
 * 1. 完全不修改现有布局文件
 * 2. 运行时动态创建吸底Tab
 * 3. 保持客户现有代码100%兼容
 * 4. 可以随时启用/禁用，不影响原有功能
 */
class NonInvasiveDynamicTabController(
    private val context: Context,
    private val coordinatorLayout: CoordinatorLayout,
    private val appBarLayout: AppBarLayout,
    private val originalTab: CoIndicator,
    private val viewPager: ViewPager2,
    private val tabTitles: List<String>
) {
    
    companion object {
        private const val TAG = "NonInvasiveDynamicTab"
        const val SCROLL_DIRECTION_UP = 1
        const val SCROLL_DIRECTION_DOWN = 2
        const val ANIMATION_DURATION = 300L
        const val SHOW_THRESHOLD = 0.7f
    }
    
    // 动态创建的组件
    private var dynamicTabContainer: LinearLayout? = null
    private var dynamicTab: TabLayout? = null
    private var tabLayoutMediator: TabLayoutMediator? = null
    
    // 状态跟踪
    private var scrollDirection = 0
    private var lastVerticalOffset = 0
    private var isBottomTabVisible = false
    private var isAnimating = false
    private var isEnabled = false
    
    // 监听器
    private var scrollListener: AppBarLayout.OnOffsetChangedListener? = null
    
    /**
     * 启用动态Tab功能
     * 这个方法是完全安全的，不会影响现有功能
     */
    fun enable() {
        if (isEnabled) {
            Log.w(TAG, "动态Tab已启用，跳过重复启用")
            return
        }
        
        Log.d(TAG, "=== 启用非侵入式动态Tab ===")
        
        try {
            createDynamicTabContainer()
            setupScrollListener()
            setupTabSync()
            
            isEnabled = true
            Log.i(TAG, "✓ 非侵入式动态Tab启用成功")
            
        } catch (e: Exception) {
            Log.e(TAG, "✗ 启用动态Tab失败", e)
            disable() // 失败时清理资源
        }
    }
    
    /**
     * 禁用动态Tab功能
     * 完全恢复到原始状态，不留任何痕迹
     */
    fun disable() {
        Log.d(TAG, "=== 禁用非侵入式动态Tab ===")
        
        // 移除滚动监听器
        scrollListener?.let { appBarLayout.removeOnOffsetChangedListener(it) }
        scrollListener = null
        
        // 销毁TabLayoutMediator
        tabLayoutMediator?.detach()
        tabLayoutMediator = null
        
        // 移除动态创建的组件
        dynamicTabContainer?.let { container ->
            (container.parent as? ViewGroup)?.removeView(container)
        }
        dynamicTabContainer = null
        dynamicTab = null
        
        // 重置状态
        isBottomTabVisible = false
        isAnimating = false
        isEnabled = false
        
        Log.i(TAG, "✓ 动态Tab已完全禁用，恢复原始状态")
    }
    
    /**
     * 运行时动态创建吸底Tab容器
     * 这是关键：不修改XML，而是在运行时添加
     */
    private fun createDynamicTabContainer() {
        Log.d(TAG, "动态创建吸底Tab容器...")
        
        // 创建容器
        dynamicTabContainer = LinearLayout(context).apply {
            orientation = LinearLayout.VERTICAL
            setBackgroundColor(context.getColor(android.R.color.white))
            elevation = 12f
            visibility = View.GONE
            alpha = 0f
        }
        
        // 创建分割线
        val divider = View(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                context.resources.getDimensionPixelSize(R.dimen.dimen_1)
            )
            setBackgroundColor(context.getColor(android.R.color.darker_gray))
            alpha = 0.2f
        }
        
        // 创建Tab
        dynamicTab = TabLayout(context).apply {
            layoutParams = LinearLayout.LayoutParams(
                LinearLayout.LayoutParams.MATCH_PARENT,
                context.resources.getDimensionPixelSize(R.dimen.dimen_48)
            )
            tabMode = TabLayout.MODE_SCROLLABLE
            tabGravity = TabLayout.GRAVITY_START
            setBackgroundColor(context.getColor(android.R.color.white))
        }
        
        // 组装容器
        dynamicTabContainer?.addView(divider)
        dynamicTabContainer?.addView(dynamicTab)
        
        // 添加到CoordinatorLayout
        val layoutParams = CoordinatorLayout.LayoutParams(
            CoordinatorLayout.LayoutParams.MATCH_PARENT,
            CoordinatorLayout.LayoutParams.WRAP_CONTENT
        ).apply {
            gravity = Gravity.BOTTOM
        }
        
        coordinatorLayout.addView(dynamicTabContainer, layoutParams)
        
        // 设置初始位置（隐藏在底部）
        dynamicTabContainer?.post {
            dynamicTabContainer?.translationY = dynamicTabContainer?.height?.toFloat() ?: 0f
        }
        
        Log.d(TAG, "动态Tab容器创建完成")
    }
    
    /**
     * 设置滚动监听器
     */
    private fun setupScrollListener() {
        scrollListener = AppBarLayout.OnOffsetChangedListener { _, verticalOffset ->
            if (!isEnabled || isAnimating) return@OnOffsetChangedListener
            
            detectScrollDirection(verticalOffset)
            handleTabVisibility(verticalOffset)
        }
        
        appBarLayout.addOnOffsetChangedListener(scrollListener)
        Log.d(TAG, "滚动监听器设置完成")
    }
    
    /**
     * 检测滚动方向
     */
    private fun detectScrollDirection(verticalOffset: Int) {
        val currentOffset = abs(verticalOffset)
        
        scrollDirection = when {
            currentOffset > lastVerticalOffset -> SCROLL_DIRECTION_UP
            currentOffset < lastVerticalOffset -> SCROLL_DIRECTION_DOWN
            else -> 0
        }
        
        lastVerticalOffset = currentOffset
    }
    
    /**
     * 处理Tab可见性
     */
    private fun handleTabVisibility(verticalOffset: Int) {
        val isOriginalTabVisible = isOriginalTabInViewport()
        val scrollPercentage = abs(verticalOffset).toFloat() / appBarLayout.totalScrollRange
        
        when (scrollDirection) {
            SCROLL_DIRECTION_UP -> {
                if (!isOriginalTabVisible && isBottomTabVisible) {
                    hideBottomTab()
                }
            }
            SCROLL_DIRECTION_DOWN -> {
                if (!isOriginalTabVisible && 
                    scrollPercentage > SHOW_THRESHOLD && 
                    !isBottomTabVisible) {
                    showBottomTab()
                } else if (isOriginalTabVisible && isBottomTabVisible) {
                    hideBottomTab()
                }
            }
        }
    }
    
    /**
     * 检查原始Tab是否可见
     */
    private fun isOriginalTabInViewport(): Boolean {
        val location = IntArray(2)
        originalTab.getLocationOnScreen(location)
        
        val screenHeight = context.resources.displayMetrics.heightPixels
        val tabTop = location[1]
        val tabBottom = tabTop + originalTab.height
        
        return tabTop >= 0 && tabBottom <= screenHeight
    }
    
    /**
     * 显示底部Tab
     */
    private fun showBottomTab() {
        val container = dynamicTabContainer ?: return
        if (isBottomTabVisible || isAnimating) return
        
        Log.d(TAG, "显示动态吸底Tab")
        isAnimating = true
        
        container.visibility = View.VISIBLE
        container.animate()
            .translationY(0f)
            .alpha(1f)
            .setDuration(ANIMATION_DURATION)
            .withEndAction {
                isBottomTabVisible = true
                isAnimating = false
            }
            .start()
    }
    
    /**
     * 隐藏底部Tab
     */
    private fun hideBottomTab() {
        val container = dynamicTabContainer ?: return
        if (!isBottomTabVisible || isAnimating) return
        
        Log.d(TAG, "隐藏动态吸底Tab")
        isAnimating = true
        
        container.animate()
            .translationY(container.height.toFloat())
            .alpha(0f)
            .setDuration(ANIMATION_DURATION)
            .withEndAction {
                container.visibility = View.GONE
                isBottomTabVisible = false
                isAnimating = false
            }
            .start()
    }
    
    /**
     * 设置Tab同步
     */
    private fun setupTabSync() {
        val bottomTab = dynamicTab ?: return
        
        // 设置Tab与ViewPager的绑定
        tabLayoutMediator = TabLayoutMediator(bottomTab, viewPager) { tab, position ->
            tab.text = tabTitles.getOrNull(position) ?: "Tab $position"
        }
        tabLayoutMediator?.attach()
        
        Log.d(TAG, "Tab同步设置完成")
    }
    
    /**
     * 获取当前状态
     */
    fun isEnabled(): Boolean = isEnabled
    
    /**
     * 获取吸底Tab是否可见
     */
    fun isBottomTabVisible(): Boolean = isBottomTabVisible
}
