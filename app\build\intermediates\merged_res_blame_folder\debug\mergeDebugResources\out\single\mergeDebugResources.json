[{"merged": "com.ttv.demo.app-debug-44:/layout_content_card.xml.flat", "source": "com.ttv.demo.app-main-46:/layout/content_card.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.ttv.demo.app-main-46:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_wallet.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_wallet.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.ttv.demo.app-main-46:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_market.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_market.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_launcher_background.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_launcher_background.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_launcher_foreground.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.ttv.demo.app-debug-44:/xml_backup_rules.xml.flat", "source": "com.ttv.demo.app-main-46:/xml/backup_rules.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_release_button_background.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/release_button_background.xml"}, {"merged": "com.ttv.demo.app-debug-44:/layout_fragment_content.xml.flat", "source": "com.ttv.demo.app-main-46:/layout/fragment_content.xml"}, {"merged": "com.ttv.demo.app-debug-44:/layout_activity_main.xml.flat", "source": "com.ttv.demo.app-main-46:/layout/activity_main.xml"}, {"merged": "com.ttv.demo.app-debug-44:/layout_floating_tab_layout.xml.flat", "source": "com.ttv.demo.app-main-46:/layout/floating_tab_layout.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_assets.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_assets.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_item_background.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/item_background.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.ttv.demo.app-debug-44:/layout_activity_main_with_navigation.xml.flat", "source": "com.ttv.demo.app-main-46:/layout/activity_main_with_navigation.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_profile.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_profile.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_home.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_home.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_home_feed_tab_indicator.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/home_feed_tab_indicator.xml"}, {"merged": "com.ttv.demo.app-debug-44:/drawable_ic_trade.xml.flat", "source": "com.ttv.demo.app-main-46:/drawable/ic_trade.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.ttv.demo.app-debug-44:/xml_data_extraction_rules.xml.flat", "source": "com.ttv.demo.app-main-46:/xml/data_extraction_rules.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.ttv.demo.app-debug-44:/layout_fragment_home_demo.xml.flat", "source": "com.ttv.demo.app-main-46:/layout/fragment_home_demo.xml"}, {"merged": "com.ttv.demo.app-debug-44:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.ttv.demo.app-main-46:/mipmap-mdpi/ic_launcher.webp"}]