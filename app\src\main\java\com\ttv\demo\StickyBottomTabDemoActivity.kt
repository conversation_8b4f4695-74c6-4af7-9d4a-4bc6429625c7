package com.ttv.demo

import android.os.Bundle
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import androidx.viewpager2.widget.ViewPager2
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import com.hbg.module.libkt.custom.indicator.CoIndicator
import com.ttv.demo.dynamic.DynamicTabController
import com.ttv.demo.dynamic.NonInvasiveDynamicTabController
import androidx.coordinatorlayout.widget.CoordinatorLayout
import com.google.android.material.appbar.AppBarLayout

/**
 * 吸底Tab演示Activity - 使用RelativeLayout布局方案
 */
class StickyBottomTabDemoActivity : AppCompatActivity() {

    // UI组件
    private lateinit var coIndicator: CoIndicator
    private lateinit var viewPager: ViewPager2
    private lateinit var coordinatorLayout: CoordinatorLayout
    private lateinit var appBarLayout: AppBarLayout

    // 动态Tab控制器
    private var dynamicTabController: DynamicTabController? = null

    // 非侵入式动态Tab控制器（推荐使用）
    private var nonInvasiveController: NonInvasiveDynamicTabController? = null

    // 数据
    private val tabTitles = listOf("推荐", "关注", "热门", "视频", "直播")

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_main)

        initViews()
        setupViewPager()
        setupDynamicTabController()  // 使用动态Tab方案
        setupBottomNavigationPadding()

        Log.d("StickyBottomTab", "=== 动态吸底Tab演示Activity初始化完成 ===")
    }

    /**
     * 初始化视图组件
     */
    private fun initViews() {
        coordinatorLayout = findViewById(R.id.clLayout)
        appBarLayout = findViewById(R.id.appBarLayout)
        coIndicator = findViewById(R.id.coIndicator)
        viewPager = findViewById(R.id.home_viewPager)

        Log.d("StickyBottomTab", "视图组件初始化完成")
        Log.d("StickyBottomTab", "CoordinatorLayout: ${coordinatorLayout != null}")
        Log.d("StickyBottomTab", "AppBarLayout: ${appBarLayout != null}")
        Log.d("StickyBottomTab", "CoIndicator: ${coIndicator != null}")
        Log.d("StickyBottomTab", "ViewPager: ${viewPager != null}")
    }

    /**
     * 设置ViewPager2和适配器
     */
    private fun setupViewPager() {
        // 设置ViewPager2适配器
        val adapter = TabFragmentAdapter(this, tabTitles)
        viewPager.adapter = adapter

        // 设置顶部CoIndicator与ViewPager2的绑定
        TabLayoutMediator(coIndicator, viewPager) { tab, position ->
            tab.text = tabTitles[position]
        }.attach()

        Log.d("StickyBottomTab", "ViewPager2设置完成，Tab数量: ${tabTitles.size}")
    }



    /**
     * 设置动态Tab控制器
     */
    private fun setupDynamicTabController() {
        Log.d("StickyBottomTab", "=== 开始设置动态Tab控制器 ===")

        try {
            // 创建动态Tab控制器
            dynamicTabController = DynamicTabController(
                context = this,
                coordinatorLayout = coordinatorLayout,
                appBarLayout = appBarLayout,
                originalTab = coIndicator,
                viewPager = viewPager,
                tabTitles = tabTitles
            )

            // 初始化控制器
            dynamicTabController?.initialize()

            // 集成业务监听器
            setupBusinessListeners()

            // 启用调试模式
            dynamicTabController?.setDebugMode(true)

            Log.i("StickyBottomTab", "✓ 动态Tab控制器设置完成")

            // 打印初始状态
            dynamicTabController?.debugStatus("初始化完成")

        } catch (e: Exception) {
            Log.e("StickyBottomTab", "✗ 动态Tab控制器设置失败", e)
        }
    }

    /**
     * 设置业务监听器
     */
    private fun setupBusinessListeners() {
        dynamicTabController?.integrateExistingListeners(
            // Tab点击监听器
            tabClickListener = { position ->
                Log.d("StickyBottomTab", "Tab点击: 位置=$position, 标题=${tabTitles.getOrNull(position)}")
            },

            // Tab选中监听器（用于埋点统计）
            tabSelectedListener = { position ->
                Log.d("StickyBottomTab", "Tab选中: 位置=$position")
                // 这里可以添加埋点统计逻辑
                when (position) {
                    0 -> Log.d("StickyBottomTab", "埋点: 推荐Tab选中")
                    1 -> Log.d("StickyBottomTab", "埋点: 关注Tab选中")
                    2 -> Log.d("StickyBottomTab", "埋点: 热门Tab选中")
                    3 -> Log.d("StickyBottomTab", "埋点: 视频Tab选中")
                    4 -> Log.d("StickyBottomTab", "埋点: 直播Tab选中")
                }
            },

            // 重复点击监听器（用于刷新数据）
            refreshDataListener = { position ->
                Log.d("StickyBottomTab", "Tab重复点击，刷新数据: 位置=$position")
                // 这里可以添加刷新数据的逻辑
                refreshTabData(position)
            }
        )
    }

    /**
     * 刷新Tab数据（模拟）
     */
    private fun refreshTabData(position: Int) {
        Log.i("StickyBottomTab", "刷新${tabTitles.getOrNull(position)}页面数据")
        // 这里可以添加实际的数据刷新逻辑
    }

    /**
     * 处理底部导航栏适配
     */
    private fun setupBottomNavigationPadding() {
        // 注意：在新架构中，我们使用DynamicTabController来处理导航栏适配
        // 这里只是获取导航栏高度用于调试信息
        val navigationBarHeight = getNavigationBarHeight()

        if (navigationBarHeight > 0) {
            Log.d("StickyBottomTab", "检测到系统导航栏高度: ${navigationBarHeight}px")
            Log.d("StickyBottomTab", "导航栏适配将由DynamicTabController自动处理")
        } else {
            Log.d("StickyBottomTab", "未检测到系统导航栏")
        }
    }

    /**
     * 获取系统导航栏高度
     */
    private fun getNavigationBarHeight(): Int {
        val resourceId = resources.getIdentifier("navigation_bar_height", "dimen", "android")
        return if (resourceId > 0) resources.getDimensionPixelSize(resourceId) else 0
    }

    /**
     * ViewPager2的Fragment适配器
     */
    private class TabFragmentAdapter(
        fragmentActivity: FragmentActivity,
        private val tabTitles: List<String>
    ) : FragmentStateAdapter(fragmentActivity) {

        override fun getItemCount(): Int = tabTitles.size

        override fun createFragment(position: Int): Fragment {
            return DemoFragment.newInstance(tabTitles[position], position)
        }
    }

    /**
     * 演示用的Fragment
     */
    class DemoFragment : Fragment() {

        companion object {
            private const val ARG_TITLE = "title"
            private const val ARG_POSITION = "position"

            fun newInstance(title: String, position: Int): DemoFragment {
                val fragment = DemoFragment()
                val args = Bundle()
                args.putString(ARG_TITLE, title)
                args.putInt(ARG_POSITION, position)
                fragment.arguments = args
                return fragment
            }
        }

        override fun onCreateView(
            inflater: LayoutInflater,
            container: ViewGroup?,
            savedInstanceState: Bundle?
        ): View? {
            val view = TextView(requireContext())
            val title = arguments?.getString(ARG_TITLE) ?: "未知"
            val position = arguments?.getInt(ARG_POSITION) ?: 0

            view.text = "这是 $title 页面\n位置: $position\n\n" +
                    "🔥 动态吸底Tab演示 🔥\n\n" +
                    "✨ 功能特性:\n" +
                    "• 向上滚动: Tab自动吸顶\n" +
                    "• 向下滚动: Tab动态吸底\n" +
                    "• 智能切换: 根据滚动方向自动显示\n" +
                    "• 平滑动画: 300ms过渡效果\n" +
                    "• 状态同步: 顶部和底部Tab保持一致\n\n" +
                    "🎯 测试方法:\n" +
                    "1. 向上快速滚动，观察Tab吸顶效果\n" +
                    "2. 向下滚动到底部，观察Tab吸底效果\n" +
                    "3. 点击吸底Tab，观察页面切换和滚动\n" +
                    "4. 重复点击同一Tab，观察刷新逻辑"
            view.textSize = 16f
            view.setPadding(32, 32, 32, 32)

            return view
        }
    }

    override fun onResume() {
        super.onResume()
        Log.d("StickyBottomTab", "Activity onResume")

        // 通知控制器生命周期变化
        dynamicTabController?.onLifecycleChanged(true)

        // 重新检测导航栏适配
        dynamicTabController?.reconfigureNavigationBarAdaptation()
    }

    override fun onPause() {
        super.onPause()
        Log.d("StickyBottomTab", "Activity onPause")

        // 通知控制器生命周期变化
        dynamicTabController?.onLifecycleChanged(false)
    }

    override fun onDestroy() {
        super.onDestroy()
        Log.d("StickyBottomTab", "Activity onDestroy")

        // 销毁动态Tab控制器
        dynamicTabController?.destroy()
        dynamicTabController = null

        // 禁用非侵入式控制器（完全恢复原始状态）
        nonInvasiveController?.disable()
        nonInvasiveController = null

        Log.d("StickyBottomTab", "=== 动态吸底Tab演示Activity销毁完成 ===")
    }

    override fun onConfigurationChanged(newConfig: android.content.res.Configuration) {
        super.onConfigurationChanged(newConfig)
        Log.d("StickyBottomTab", "配置变化: ${newConfig.orientation}")

        // 通知控制器配置变化
        dynamicTabController?.onConfigurationChanged()
    }

    /**
     * 调试方法：手动触发状态检查
     */
    private fun debugControllerStatus() {
        dynamicTabController?.debugStatus("手动检查")
    }

    /**
     * 测试方法：模拟各种场景
     */
    private fun testScenarios() {
        Log.d("StickyBottomTab", "=== 开始场景测试 ===")

        // 测试页面切换
        for (i in tabTitles.indices) {
            dynamicTabController?.onPageChanged(i)
        }

        // 测试配置变化
        dynamicTabController?.onConfigurationChanged()

        // 测试生命周期变化
        dynamicTabController?.onLifecycleChanged(false)
        dynamicTabController?.onLifecycleChanged(true)

        Log.d("StickyBottomTab", "=== 场景测试完成 ===")
    }
}